{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C5038", "message": {"text": "membro de dados 'AAURACRONPCGIsland::UpdateInterval' será inicializado após membro de dados 'AAURACRONPCGIsland::StreamableManager'"}, "analysisTarget": {"uri": "file:///C:/AURACRON/Source/AURACRON/Private/PCG/AURACRONPCGIsland.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/AURACRON/Source/AURACRON/Private/PCG/AURACRONPCGIsland.cpp"}, "region": {"startLine": 46, "startColumn": 7, "snippet": {"text": "    , UpdateInterval(0.1f) // Otimizado para performance - Timer ao invés de Tick"}}}}]}, {"ruleId": "C5038", "message": {"text": "membro de dados 'AAURACRONPCGIsland::bHasIlhaCentralAuracron' será inicializado após membro de dados 'AAURACRONPCGIsland::SolarTrilhoIntensity'"}, "analysisTarget": {"uri": "file:///C:/AURACRON/Source/AURACRON/Private/PCG/AURACRONPCGIsland.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/AURACRON/Source/AURACRON/Private/PCG/AURACRONPCGIsland.cpp"}, "region": {"startLine": 53, "startColumn": 7, "snippet": {"text": "    , bHasIlhaCentralAuracron(false)"}}}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}