// AURACRONPCGTrail.h
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Classe para gerenciar as trilhas dinâmicas

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
// Forward declaration para evitar dependência direta
class UPCGComponent;
#include "PCGSettings.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "Components/SplineComponent.h"
#include "Components/BoxComponent.h"
#include "Components/PointLightComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "GameFramework/Character.h"
#include "Curves/CurveFloat.h"
#include "Curves/CurveLinearColor.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"
#include "AURACRONPCGTrail.generated.h"

class UPCGComponent;
class USplineComponent;
class UNiagaraComponent;

// Enum para capacidade de hardware
UENUM(BlueprintType)
enum class EAURACRONHardwareCapacity : uint8
{
    Entry   UMETA(DisplayName = "Entry Level"),
    Mid     UMETA(DisplayName = "Mid Level"),
    High    UMETA(DisplayName = "High End")
};

// Delegate para eventos de entrada de jogador na trilha
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerEnterTrailSignature, ACharacter*, Player);

/**
 * Classe base para todos os tipos de Trails
 */
UCLASS()
class AURACRON_API ATrailBase : public AActor
{
    GENERATED_BODY()
    
public:
    ATrailBase();
    
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    
    // Ativa/desativa o trail
    UFUNCTION(BlueprintCallable, Category = "Trail")
    virtual void SetTrailActive(bool bActive);
    
    // Configurar trilho para Fase 1: Despertar (50% de poder)
    UFUNCTION(BlueprintCallable, Category = "Trail")
    virtual void ConfigureForAwakeningPhase(bool bIsEntryDevice);
    
    // Configurar trilho para Fase 2: Convergência (poder baseado na capacidade do dispositivo)
    UFUNCTION(BlueprintCallable, Category = "Trail")
    virtual void ConfigureForConvergencePhase(bool bIsEntryDevice, bool bIsMidDevice, bool bIsHighDevice);
    
    // Definir percentual de poder do trilho (0.0 a 1.0)
    UFUNCTION(BlueprintCallable, Category = "Trail")
    virtual void SetPowerPercentage(float PowerPercentage);
    
    // Aplica efeito ao jogador quando está no trail
    UFUNCTION()
    virtual void ApplyTrailEffect(AActor* OverlappingActor);

    // Funções de callback para colisão
    UFUNCTION()
    virtual void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                               UPrimitiveComponent* OtherComp, int32 OtherBodyIndex,
                               bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    virtual void OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                             UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

    // Atualiza efeitos visuais do trail
    virtual void UpdateTrailEffects(float DeltaTime);

    // Atualiza propriedades do trail (implementação específica por tipo)
    virtual void UpdateTrailProperties() {}

protected:
    // Sistema de partículas Niagara para efeitos visuais
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Trail")
    UNiagaraComponent* TrailEffectComponent;
    
    // Componente de colisão para detectar jogadores
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Trail")
    UBoxComponent* CollisionComponent;
    
    // Spline que define o caminho do trail
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Trail")
    USplineComponent* SplineComponent;
    
    // Luz dinâmica para iluminação do trail
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Trail")
    UPointLightComponent* TrailLightComponent;

    // Componente Niagara principal para trilhas
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Trail")
    UNiagaraComponent* TrailNiagaraComponent;

    // Estado do trail
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trail")
    bool bIsActive = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trail")
    bool bIsVisible = true;

    // Tipo do trail
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trail")
    EAURACRONTrailType TrailType = EAURACRONTrailType::None;
    

    
    // Percentual de poder do trail (0.0 a 1.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trail", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float PowerPercentage;
    
    // Efeitos visuais adaptados ao hardware
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trail")
    bool bAdaptVisualEffectsToHardware;


};

/**
 * Implementação específica do Solar Trail
 */
UCLASS()
class AURACRON_API ASolarTrail : public ATrailBase
{
    GENERATED_BODY()
    
public:
    ASolarTrail();
    
    virtual void Tick(float DeltaTime) override;
    virtual void ApplyTrailEffect(AActor* OverlappingActor) override;
    
protected:
    // Intensidade da luz baseada na posição do sol
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Solar Trail")
    UCurveFloat* SunPositionIntensityCurve;
    
    // Cor da luz baseada na hora do dia
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Solar Trail")
    UCurveLinearColor* DaytimeColorCurve;
    
    // Atualiza a intensidade e cor baseado na hora do dia
    void UpdateTrailProperties();
};

/**
 * Implementação específica do Axis Trail
 */
UCLASS()
class AURACRON_API AAxisTrail : public ATrailBase
{
    GENERATED_BODY()
    
public:
    AAxisTrail();
    
    virtual void Tick(float DeltaTime) override;
    virtual void ApplyTrailEffect(AActor* OverlappingActor) override;
    
protected:
    // Força do efeito de atração/repulsão
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Axis Trail")
    float ForceStrength;
    
    // Raio de efeito
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Axis Trail")
    float EffectRadius;
    
    // Se o trail atrai (true) ou repele (false)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Axis Trail")
    bool bAttractMode;
    
    // Aplica força de atração/repulsão aos atores próximos
    void ApplyForceToNearbyActors();
};

/**
 * Implementação específica do Lunar Trail
 */
UCLASS()
class AURACRON_API ALunarTrail : public ATrailBase
{
    GENERATED_BODY()
    
public:
    ALunarTrail();
    
    virtual void Tick(float DeltaTime) override;
    virtual void ApplyTrailEffect(AActor* OverlappingActor) override;
    
protected:
    // Intensidade do efeito de invisibilidade
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lunar Trail")
    float InvisibilityStrength;
    
    // Duração do efeito após sair do trail
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lunar Trail")
    float EffectDuration;
    
    // Velocidade de movimento adicional
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lunar Trail")
    float MovementSpeedBonus;
    
    // Aplica efeito de invisibilidade ao jogador
    void ApplyInvisibilityEffect(ACharacter* Character);
};

/**
 * Ator para gerenciar uma trilha dinâmica específica no AURACRON
 * Cada tipo de trilha (Prismal Flow, Ethereal Path, Nexus Connection) terá sua própria instância
 * Integra o sistema PCG com o sistema de trilhas
 */
UCLASS()
class AURACRON_API AAURACRONPCGTrail : public ATrailBase
{
    GENERATED_BODY()

public:
    AAURACRONPCGTrail();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Configurar o tipo de trilha
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail")
    void SetTrailType(EAURACRONTrailType NewType);
    
    // Evento disparado quando um jogador entra na trilha
    UPROPERTY(BlueprintAssignable, Category = "Trail|Events")
    FOnPlayerEnterTrailSignature OnPlayerEnterTrailEvent;
    
    // Evento disparado quando um jogador entra em um Solar Trail
    UPROPERTY(BlueprintAssignable, Category = "Trail|Events|Solar")
    FOnPlayerEnterTrailSignature OnApplySolarEffectEvent;
    
    // Evento disparado quando um jogador entra em um Axis Trail
    UPROPERTY(BlueprintAssignable, Category = "Trail|Events|Axis")
    FOnPlayerEnterTrailSignature OnApplyAxisEffectEvent;
    
    // Evento disparado quando um jogador entra em um Lunar Trail
    UPROPERTY(BlueprintAssignable, Category = "Trail|Events|Lunar")
    FOnPlayerEnterTrailSignature OnApplyLunarEffectEvent;

    // Obter o tipo de trilha
    UFUNCTION(BlueprintPure, Category = "PCG|Trail")
    EAURACRONTrailType GetTrailType() const { return TrailType; }

    // Gerar a trilha procedural
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail")
    void GenerateTrail();

    // Atualizar a trilha com base na fase do mapa
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);

    // Definir a visibilidade da trilha
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail")
    void SetTrailVisibility(bool bVisible);

    // Definir a escala de atividade da trilha (0.0 = preview, 1.0 = totalmente ativo)
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail")
    void SetActivityScale(float Scale);

    // Definir os pontos de início e fim da trilha
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail")
    void SetTrailEndpoints(const FVector& StartPoint, const FVector& EndPoint);

    // Funções de performance e hardware
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Performance")
    float GetParticleBudgetForTrailType(EAURACRONTrailType Type);

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Performance")
    EAURACRONHardwareCapacity DetermineHardwareCapacity();

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Performance")
    void ApplyParticleBudgetScaling();

    // Funções de efeitos específicos por tipo de trilha
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Effects")
    void ApplySolarEffectsToPlayer(ACharacter* Player, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Effects")
    void ApplyAxisEffectsToPlayer(ACharacter* Player, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Effects")
    void ApplyLunarEffectsToPlayer(ACharacter* Player, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Effects")
    void ApplyPrismalFlowEffectsToPlayer(ACharacter* Player, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Effects")
    void ApplyEtherealPathEffectsToPlayer(ACharacter* Player, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Effects")
    void ApplyNexusConnectionEffectsToPlayer(ACharacter* Player, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Effects")
    void RemoveTrailEffectsFromPlayer(ACharacter* Player);

    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Effects")
    void ApplyGameplayEffectToPlayer(ACharacter* Player, const FString& EffectName, float Duration);

    // Funções de atualização e propriedades
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|Update")
    void UpdateTrailProperties();
    
    // Efeitos Niagara para os diferentes tipos de trilhas (Assets)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Effects")
    UNiagaraSystem* SolarTrailEffectAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Effects")
    UNiagaraSystem* AxisTrailEffectAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Effects")
    UNiagaraSystem* LunarTrailEffectAsset;

    // Componentes Niagara para os diferentes tipos de trilhas (Instâncias)
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG|Effects")
    UNiagaraComponent* SolarTrailEffect;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG|Effects")
    UNiagaraComponent* AxisTrailEffect;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG|Effects")
    UNiagaraComponent* LunarTrailEffect;


    
    // Mesh para pontos de conexão do Axis Trail
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|Meshes")
    UStaticMesh* AxisConnectionMesh;
    
    // Integração com World Partition - Configura streaming para a trilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|WorldPartition")
    void ConfigureWorldPartitionStreaming(const FAURACRONPCGStreamingConfig& StreamingConfig);
    
    // Integração com Data Layers - Associa a trilha a uma Data Layer específica
    UFUNCTION(BlueprintCallable, Category = "PCG|Trail|DataLayers")
    void AssociateWithDataLayer(const FName& DataLayerName);

    
    // Retorna a escala de atividade atual
    UFUNCTION(BlueprintPure, Category = "PCG|Trail")
    float GetActivityScale() const { return ActivityScale; }
    
    // Retorna se a trilha está visível
    UFUNCTION(BlueprintPure, Category = "PCG|Trail")
    bool IsVisible() const { return bIsVisible; }
    
    // Adicionar um ponto de controle à trilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void AddControlPoint(const FVector& ControlPoint);

    // Limpar todos os pontos de controle
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void ClearControlPoints();

public:
    // Distância total da trilha
    UPROPERTY(BlueprintReadOnly, Category = "PCG|Trail")
    float TrailDistancePublic;

    // Componente PCG principal para geração da trilha
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "PCG|Components")
    UPCGComponent* PCGComponent;

    // Componente Spline para definir o caminho da trilha PCG
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "PCG|Components")
    USplineComponent* PCGSplineComponent;

    // Configurações PCG para esta trilha
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG|Settings")
    UPCGSettings* TrailSettings;

    // Características específicas da trilha
    // Prismal Flow
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|PrismalFlow", meta = (EditCondition = "TrailType == EAURACRONTrailType::PrismalFlow"))
    float FlowIntensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|PrismalFlow", meta = (EditCondition = "TrailType == EAURACRONTrailType::PrismalFlow"))
    float FlowWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|PrismalFlow", meta = (EditCondition = "TrailType == EAURACRONTrailType::PrismalFlow"))
    float FlowSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|PrismalFlow", meta = (EditCondition = "TrailType == EAURACRONTrailType::PrismalFlow"))
    bool bHasFlowObstacles;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|PrismalFlow", meta = (EditCondition = "TrailType == EAURACRONTrailType::PrismalFlow"))
    FLinearColor FlowColor;

    // Ethereal Path
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|EtherealPath", meta = (EditCondition = "TrailType == EAURACRONTrailType::EtherealPath"))
    float PathVisibility;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|EtherealPath", meta = (EditCondition = "TrailType == EAURACRONTrailType::EtherealPath"))
    float PathWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|EtherealPath", meta = (EditCondition = "TrailType == EAURACRONTrailType::EtherealPath"))
    float PathFluctuation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|EtherealPath", meta = (EditCondition = "TrailType == EAURACRONTrailType::EtherealPath"))
    float PathAlpha;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|EtherealPath", meta = (EditCondition = "TrailType == EAURACRONTrailType::EtherealPath"))
    bool bHasPathGuides;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|EtherealPath", meta = (EditCondition = "TrailType == EAURACRONTrailType::EtherealPath"))
    FLinearColor PathColor;

    // Nexus Connection
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|NexusConnection", meta = (EditCondition = "TrailType == EAURACRONTrailType::NexusConnection"))
    float ConnectionStrength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|NexusConnection", meta = (EditCondition = "TrailType == EAURACRONTrailType::NexusConnection"))
    float ConnectionWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|NexusConnection", meta = (EditCondition = "TrailType == EAURACRONTrailType::NexusConnection"))
    float ConnectionPulseRate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|NexusConnection", meta = (EditCondition = "TrailType == EAURACRONTrailType::NexusConnection"))
    bool bHasConnectionNodes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|PCG|NexusConnection", meta = (EditCondition = "TrailType == EAURACRONTrailType::NexusConnection"))
    FLinearColor ConnectionColor;

    // Componente de colisão para interação com jogadores
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|PCG")
    UBoxComponent* InteractionVolume;

    // Eventos de interação com jogadores (UE 5.6 API moderna)
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void HandlePlayerOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void HandlePlayerEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

    // Aplicar efeitos ao jogador baseado no tipo de trilha
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void ApplyTrailEffectsToPlayer(ACharacter* Player, float DeltaTime);

    // Verificar se um jogador está dentro da trilha
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    bool IsPlayerInTrail(ACharacter* Player) const;

    // Obter a posição relativa do jogador na trilha (0.0 = início, 1.0 = fim)
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    float GetPlayerPositionAlongTrail(ACharacter* Player) const;

private:
    // Tipo de trilha PCG específico
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG")
    EAURACRONTrailType PCGTrailType;

    // Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)
    UPROPERTY()
    float ActivityScale;

    // Array para armazenar elementos gerados dinamicamente
    UPROPERTY()
    TArray<UActorComponent*> GeneratedElements;

    // Tempo decorrido desde a última atualização da trilha
    UPROPERTY()
    float TimeSinceLastUpdate;

    // Intervalo de atualização da trilha em segundos
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG")
    float UpdateInterval;

    // Armazenar os pontos de início e fim da trilha
    UPROPERTY()
    FVector StartLocation;

    UPROPERTY()
    FVector EndLocation;

    // Pontos da spline para cálculos
    UPROPERTY()
    TArray<FVector> SplinePoints;

    // Valores padrão para as propriedades da trilha
    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PrismalFlow")
    float DefaultFlowWidth;

    // Propriedades de streaming e data layer
    UPROPERTY()
    FAURACRONPCGStreamingConfig StreamingConfiguration;

    UPROPERTY()
    bool bStreamingEnabled;

    UPROPERTY()
    float StreamingDistance;

    UPROPERTY()
    FName AssociatedDataLayer;

    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PrismalFlow")
    float DefaultFlowSpeed;

    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|PrismalFlow")
    float DefaultFlowIntensity;

    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|EtherealPath")
    float DefaultPathWidth;

    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|EtherealPath")
    float DefaultPathVisibility;

    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|EtherealPath")
    float DefaultPathFluctuation;

    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|NexusConnection")
    float DefaultConnectionWidth;

    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|NexusConnection")
    float DefaultConnectionStrength;

    UPROPERTY(EditAnywhere, Category = "AURACRON|PCG|NexusConnection")
    float DefaultConnectionPulseRate;

    // Lista de jogadores// Jogadores atualmente na trilha
    UPROPERTY()
    TArray<ACharacter*> OverlappingPlayers;
    
    // Jogadores com efeito de invisibilidade aplicado (Lunar Trail)
    UPROPERTY()
    TArray<ACharacter*> InvisiblePlayers;
    
    // Armazena as velocidades originais dos jogadores (Solar Trail)
    UPROPERTY()
    TMap<ACharacter*, float> OriginalPlayerSpeeds;

    // Contador de jogadores na trilha
    UPROPERTY()
    int32 NumOverlappingPlayers;

    // Propriedades para sistema de streaming e performance (implementação no CPP)
    struct FStreamableManager* StreamableManager;

    UPROPERTY()
    FTimerHandle UpdateTimerHandle;

    UPROPERTY()
    float TrailUpdateInterval;

    // Propriedades já definidas em outras seções - removidas para evitar duplicação



    // Funções internas para geração de características específicas (legado)
    void GeneratePrismalFlow();
    void GenerateEtherealPath();
    void GenerateNexusConnection();

    // Funções modernas para geração de trilhas
    void ClearTrail();
    TArray<FVector> GenerateTrailPointsModern();
    void GenerateSolarTrail();
    void GenerateAxisTrail();
    void GenerateLunarTrail();
    void ApplyActivityScale();

    // Funções de utilidade para cálculos de trilha
    void CalculateSplinePoints();
    void UpdateTrailParameters();

    /** Atualizar o volume de interação para seguir a spline */
    void UpdateInteractionVolume();

    /** Função auxiliar para configurar parâmetros PCG usando API moderna UE 5.6 */
    void SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Context = TEXT(""));

public:
    /** Obter componente PCG para acesso externo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    UPCGComponent* GetPCGComponent() const { return PCGComponent; }
    
    /** Atualizar conexões entre a trilha e os objetivos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void UpdateObjectiveConnections(const TArray<FVector>& ObjectivePositions, EAURACRONMapPhase MapPhase);

    // Eventos para efeitos de trilha
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnAxisTransitionAvailable, ACharacter*, Character, FVector, Destination, int32, ConnectionPoint);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSolarHealthRegeneration, AActor*, Player, float, HealthRegen, float, SpeedBoost);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnLunarVisionEffect, APawn*, Pawn, float, NightVisionFactor, float, StealthFactor);

    UPROPERTY(BlueprintAssignable, Category = "AURACRON|Trail Events")
    FOnAxisTransitionAvailable OnAxisTransitionAvailable;

    UPROPERTY(BlueprintAssignable, Category = "AURACRON|Trail Events")
    FOnSolarHealthRegeneration OnSolarHealthRegeneration;

    UPROPERTY(BlueprintAssignable, Category = "AURACRON|Trail Events")
    FOnLunarVisionEffect OnLunarVisionEffect;

    /** Aplicar contração do mapa à trilha */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Trail")
    void OnMapContraction(float ContractionFactor);
};